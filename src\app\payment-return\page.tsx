"use client";

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { loadStripe } from '@stripe/stripe-js';
import { CheckCircle2, XCircle, Loader2 } from 'lucide-react';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function PaymentReturn() {
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);

  useEffect(() => {
    const sessionIdParam = searchParams.get('session_id');
    const transactionIdParam = searchParams.get('transaction_id');
    const orderIdParam = searchParams.get('order_id');

    setSessionId(sessionIdParam);
    setTransactionId(transactionIdParam);
    setOrderId(orderIdParam);

    if (!sessionIdParam) {
      setStatus('error');
      setMessage('No session ID found in URL parameters.');
      return;
    }

    const checkPaymentStatus = async () => {
      try {
        const stripe = await stripePromise;
        if (!stripe) {
          throw new Error('Stripe failed to load');
        }

        // Retrieve the checkout session to check its status
        const { error } = await stripe.retrieveCheckoutSession(sessionIdParam);
        
        if (error) {
          console.error('Error retrieving checkout session:', error);
          setStatus('error');
          setMessage(error.message || 'Payment verification failed');
        } else {
          setStatus('success');
          setMessage('Payment completed successfully! Your order has been processed.');
          
          // Redirect to success page after a short delay
          setTimeout(() => {
            window.location.href = '/checkout?payment=success';
          }, 3000);
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
        setStatus('error');
        setMessage('Failed to verify payment status. Please contact support if you were charged.');
      }
    };

    checkPaymentStatus();
  }, [searchParams]);

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="text-center">
            <Loader2 className="h-16 w-16 animate-spin text-primary mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Processing Payment</h1>
            <p className="text-gray-600">Please wait while we verify your payment...</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="text-center">
            <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            {transactionId && (
              <p className="text-sm text-gray-500 mb-2">Transaction ID: {transactionId}</p>
            )}
            {orderId && (
              <p className="text-sm text-gray-500 mb-4">Order ID: {orderId}</p>
            )}
            <p className="text-sm text-gray-500">Redirecting you to the checkout page...</p>
          </div>
        );
      
      case 'error':
        return (
          <div className="text-center">
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Error</h1>
            <p className="text-gray-600 mb-4">{message}</p>
            <button
              onClick={() => window.location.href = '/checkout'}
              className="bg-primary text-white px-6 py-2 rounded-full hover:bg-primary/90 transition-colors"
            >
              Return to Checkout
            </button>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        {renderContent()}
      </div>
    </div>
  );
}
