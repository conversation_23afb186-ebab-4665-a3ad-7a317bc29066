"use client";

import React, { useState } from 'react';
import EmbeddedCheckoutModal from '@/components/payment/EmbeddedCheckout';

export default function TestEmbeddedCheckout() {
  const [isOpen, setIsOpen] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const createTestCheckout = async () => {
    setIsLoading(true);
    try {
      // Test data for creating embedded checkout
      const testData = {
        userId: "test-user-123",
        userEmail: "<EMAIL>",
        sellerId: "test-seller-123",
        sellerEmail: "<EMAIL>",
        sellerStripeAccountId: "acct_test123",
        orderId: "test-order-" + Date.now(),
        amount: 2000, // $20.00 in cents
        currency: "usd",
        productName: "Test Product",
        productDescription: "Test embedded checkout product",
      };

      const response = await fetch("/api/escrow/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testData),
      });

      const result = await response.json();
      console.log("API Response:", result);

      if (response.ok && result.success && result.clientSecret) {
        setClientSecret(result.clientSecret);
        setIsOpen(true);
      } else {
        alert("Failed to create checkout session: " + (result.error || "Unknown error"));
      }
    } catch (error) {
      console.error("Error creating checkout:", error);
      alert("Error creating checkout session");
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplete = (result: any) => {
    console.log("Payment completed:", result);
    alert("Payment completed successfully!");
    setIsOpen(false);
    setClientSecret(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-center mb-6">Test Embedded Checkout</h1>
        
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            <p><strong>Test Amount:</strong> $20.00</p>
            <p><strong>Payment Methods:</strong> Card, Apple Pay, Google Pay, Link</p>
          </div>
          
          <button
            onClick={createTestCheckout}
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? "Creating Checkout..." : "Test Embedded Checkout"}
          </button>
          
          <div className="text-xs text-gray-500 text-center">
            This will create a test embedded checkout session with multiple payment methods
          </div>
        </div>
      </div>

      <EmbeddedCheckoutModal
        isOpen={isOpen}
        onClose={() => {
          setIsOpen(false);
          setClientSecret(null);
        }}
        clientSecret={clientSecret}
        onComplete={handleComplete}
      />
    </div>
  );
}
