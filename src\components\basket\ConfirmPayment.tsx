import React, { useState } from "react";
import { CheckSquare, Square, ChevronDown, HelpCircle } from "react-feather";
import { <PERSON><PERSON>, ModalContent, ModalBody } from "@heroui/react";
import { Button } from "../ui/button";
import { BasketItem } from "../../types/basket";
import EmbeddedCheckoutModal from "../payment/EmbeddedCheckout";

interface ConfirmPaymentProps {
  selectedItem: BasketItem | null;
  onConfirm: () => void;
  currencySymbol?: string; // Added currency symbol prop
  profileDetails?: {
    email?: any;
    stripe_id?: any;
    id: string;
    avatar?: string;
    profile_name?: string;
    currency?: string;
  } | null;
  userProfileDetails?: {
    email?: any;
    profile_name?: any;
    id: string;
  } | null;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: React.ReactNode;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: "stripe",
    name: "Card, Apple Pay, Google Pay & More",
    icon: <img src="/assets/stripe.svg" alt="" className="w-4 h-4 object-cover" />,
  },
  // {
  //   id: "crypto",
  //   name: "Crypto",
  //   icon: <img src="/assets/stripe.svg" alt="Crypto" className="w-4 h-4 object-cover" />,
  // },
];

const ConfirmPayment: React.FC<ConfirmPaymentProps> = ({
  selectedItem,
  onConfirm,
  currencySymbol = "$", // Default to $ if not provided
  profileDetails,
  userProfileDetails,
}) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isEmbeddedCheckoutOpen, setIsEmbeddedCheckoutOpen] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [checked, setChecked] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(
    paymentMethods[0]
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  if (!selectedItem) return null;

  const transactionFee = selectedItem.subtotal * 0.04;
  const total = selectedItem.subtotal + transactionFee;

  const handlePaymentConfirm = async () => {
    setIsProcessing(true);
    try {
      // Check if we have the required data
      if (!selectedItem || !profileDetails || !userProfileDetails) {
        console.error("Missing required data:");
        return;
      }

      // Prepare escrow payment data
      const escrowData = {
        userId: userProfileDetails.id,
        userEmail: userProfileDetails.email,
        sellerId: profileDetails.id,
        sellerEmail: profileDetails.email,
        sellerStripeAccountId: profileDetails.stripe_id,
        orderId: selectedItem.orderId || selectedItem.id.toString(),
        amount: Math.round(total * 100), // Convert to cents
        currency: profileDetails.currency || "usd",
        productName: selectedItem.title,
        productDescription: selectedItem.description || selectedItem.title || "Service order",
      };

      // Call the escrow API to create embedded checkout session
      const response = await fetch("/api/escrow/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(escrowData),
      });

      const result = await response.json();

      if (response.ok && result.success && result.clientSecret) {
        console.log("✅ Embedded checkout session created successfully!");

        // Set the client secret and open embedded checkout
        setClientSecret(result.clientSecret);
        setIsPaymentModalOpen(false);
        setIsEmbeddedCheckoutOpen(true);
      } else {
        console.error("❌ Escrow payment failed:", result.error || result);
      }
    } catch (error) {
      console.error("❌ Error in handlePaymentConfirm:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleEmbeddedCheckoutComplete = (result: any) => {
    console.log("Embedded checkout completed:", result);
    setIsEmbeddedCheckoutOpen(false);
    setClientSecret(null);
    onConfirm();
  };

  return (
    <div className="flex flex-col mr-4">
      <div className="flex justify-between">
        <p className="text-[#404040]">Subtotal</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {selectedItem.subtotal.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between my-1">
        <p className="text-[#404040]">Transaction fee (4%)</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {transactionFee.toFixed(2)}
        </p>
      </div>
      <div className="flex justify-between mb-4">
        <p className="text-[#404040]">Order total</p>
        <p className="font-bold text-primary">
          {currencySymbol}
          {total.toFixed(2)}
        </p>
      </div>

      <div className="flex justify-between text-subtitle mb-2">
        <p>Delivery time</p>
        <p className="font-semibold">{selectedItem.time}</p>
      </div>

      <div className="flex flex-row gap-2 mt-2 border-b-2 pb-3">
        <div onClick={() => setChecked((prev) => !prev)} className="cursor-pointer select-none">
          {checked ? <CheckSquare color="#333333" /> : <Square color="#bdbdbd" />}
        </div>
        <div>
          <p className="text-primary">Request an invoice </p>
          <p className="text-[#898887]">
            Note: to obtain an Invoice you'll need to provide your tax details (legal name, address
            and VAT registration number).
          </p>
        </div>
      </div>
      <div className="mt-3">
        <p className="text-subtitle">
          Terms: By placing your order, you confirm that you agree to the User Terms and Conditions.
        </p>
        <button
          onClick={() => setIsPaymentModalOpen(true)}
          disabled={isProcessing}
          className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? "Processing..." : "Confirm Payment"}
        </button>
      </div>

      <Modal
        placement="auto"
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        hideCloseButton={true}
      >
        <ModalContent className="modal-content w-xl p-12 rounded-3xl">
          {() => (
            <>
              <ModalBody>
                <div className="flex gap-2 items-center">
                  <p className="font-bold text-lg max-md:font-semibold max-md:text-base">
                    Select Preferred Payment Option
                  </p>
                  <HelpCircle className="h-5 w-5 text-gray-400 transition-transform" />
                </div>
                {/* <p className="text-center text-black text-lg">
                  {orderPlacedSuccessfully
                    ? "Order placed successfully!"
                    : `Confirm payment of ${currencySymbol}${total.toFixed(2)}?`}
                </p> */}

                {/* Payment Method Dropdown */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Method
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="w-full bg-white border border-gray-300 rounded-lg px-4 py-2 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center">
                            {selectedPaymentMethod.icon}
                          </div>
                          <span className="text-gray-900">{selectedPaymentMethod.name}</span>
                        </div>
                        <ChevronDown
                          className={`h-5 w-5 text-gray-400 transition-transform ${
                            isDropdownOpen ? "rotate-180" : ""
                          }`}
                        />
                      </div>
                    </button>

                    {isDropdownOpen && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg">
                        {paymentMethods.map((method) => (
                          <button
                            key={method.id}
                            type="button"
                            onClick={() => {
                              setSelectedPaymentMethod(method);
                              setIsDropdownOpen(false);
                            }}
                            className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3 ${
                              selectedPaymentMethod.id === method.id
                                ? "bg-blue-50 text-primary"
                                : "text-gray-900"
                            } ${method.id === paymentMethods[0].id ? "rounded-t-lg" : ""} ${
                              method.id === paymentMethods[paymentMethods.length - 1].id
                                ? "rounded-b-lg"
                                : ""
                            }`}
                          >
                            <div className="flex items-center justify-center">{method.icon}</div>
                            <span>{method.name}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-5 border-black text-black border-2 py-5 text-base disabled:opacity-50"
                    onClick={handlePaymentConfirm}
                    disabled={isProcessing}
                  >
                    {isProcessing ? "Creating checkout..." : "Yes, proceed with payment"}
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-full w-full mt-3 border-black text-black border-2 py-5 text-base"
                    onClick={() => setIsPaymentModalOpen(false)}
                  >
                    No, cancel
                  </Button>
                </div>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Embedded Checkout Modal */}
      <EmbeddedCheckoutModal
        isOpen={isEmbeddedCheckoutOpen}
        onClose={() => {
          setIsEmbeddedCheckoutOpen(false);
          setClientSecret(null);
        }}
        clientSecret={clientSecret}
        onComplete={handleEmbeddedCheckoutComplete}
      />
    </div>
  );
};

export default ConfirmPayment;
