import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout,
} from '@stripe/react-stripe-js';
import { Modal, ModalContent, ModalBody, ModalHeader } from "@heroui/react";
import { X, Loader2 } from "lucide-react";

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface EmbeddedCheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  clientSecret: string | null;
  onComplete?: (result: any) => void;
}

const EmbeddedCheckoutModal: React.FC<EmbeddedCheckoutModalProps> = ({
  isOpen,
  onClose,
  clientSecret,
  onComplete
}) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (clientSecret) {
      setIsLoading(false);
    }
  }, [clientSecret]);

  const handleComplete = (result: any) => {
    console.log('Payment completed:', result);
    if (onComplete) {
      onComplete(result);
    }
    onClose();
  };

  if (!clientSecret) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      placement="center"
      hideCloseButton={true}
      classNames={{
        base: "max-h-[90vh]",
        body: "p-0",
      }}
    >
      <ModalContent>
        <ModalHeader className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Complete Your Payment</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </ModalHeader>
        <ModalBody className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-gray-600">Loading payment form...</span>
            </div>
          ) : (
            <div className="min-h-[500px]">
              <EmbeddedCheckoutProvider
                stripe={stripePromise}
                options={{
                  clientSecret,
                  onComplete: handleComplete,
                }}
              >
                <EmbeddedCheckout />
              </EmbeddedCheckoutProvider>
            </div>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default EmbeddedCheckoutModal;
